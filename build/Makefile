# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named sdl3_pong

# Build rule for target.
sdl3_pong: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sdl3_pong
.PHONY : sdl3_pong

# fast build rule for target.
sdl3_pong/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/build
.PHONY : sdl3_pong/fast

sdl3_pong_GPU.o: sdl3_pong_GPU.c.o
.PHONY : sdl3_pong_GPU.o

# target to build an object file
sdl3_pong_GPU.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o
.PHONY : sdl3_pong_GPU.c.o

sdl3_pong_GPU.i: sdl3_pong_GPU.c.i
.PHONY : sdl3_pong_GPU.i

# target to preprocess a source file
sdl3_pong_GPU.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.i
.PHONY : sdl3_pong_GPU.c.i

sdl3_pong_GPU.s: sdl3_pong_GPU.c.s
.PHONY : sdl3_pong_GPU.s

# target to generate assembly for a file
sdl3_pong_GPU.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.s
.PHONY : sdl3_pong_GPU.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... sdl3_pong"
	@echo "... sdl3_pong_GPU.o"
	@echo "... sdl3_pong_GPU.i"
	@echo "... sdl3_pong_GPU.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

