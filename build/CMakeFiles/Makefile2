# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/sdl3_pong.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/sdl3_pong.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/sdl3_pong.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/sdl3_pong.dir

# All Build rule for target.
CMakeFiles/sdl3_pong.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles --progress-num=1,2 "Built target sdl3_pong"
.PHONY : CMakeFiles/sdl3_pong.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/sdl3_pong.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/sdl3_pong.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles 0
.PHONY : CMakeFiles/sdl3_pong.dir/rule

# Convenience name for target.
sdl3_pong: CMakeFiles/sdl3_pong.dir/rule
.PHONY : sdl3_pong

# codegen rule for target.
CMakeFiles/sdl3_pong.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles --progress-num=1,2 "Finished codegen for target sdl3_pong"
.PHONY : CMakeFiles/sdl3_pong.dir/codegen

# clean rule for target.
CMakeFiles/sdl3_pong.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sdl3_pong.dir/build.make CMakeFiles/sdl3_pong.dir/clean
.PHONY : CMakeFiles/sdl3_pong.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

