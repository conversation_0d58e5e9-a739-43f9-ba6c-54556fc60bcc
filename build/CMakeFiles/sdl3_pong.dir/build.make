# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build

# Include any dependencies generated for this target.
include CMakeFiles/sdl3_pong.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sdl3_pong.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sdl3_pong.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sdl3_pong.dir/flags.make

CMakeFiles/sdl3_pong.dir/codegen:
.PHONY : CMakeFiles/sdl3_pong.dir/codegen

CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o: CMakeFiles/sdl3_pong.dir/flags.make
CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o: /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/sdl3_pong_GPU.c
CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o: CMakeFiles/sdl3_pong.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o -MF CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o.d -o CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o -c /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/sdl3_pong_GPU.c

CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/sdl3_pong_GPU.c > CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.i

CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/sdl3_pong_GPU.c -o CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.s

# Object files for target sdl3_pong
sdl3_pong_OBJECTS = \
"CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o"

# External object files for target sdl3_pong
sdl3_pong_EXTERNAL_OBJECTS =

sdl3_pong: CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o
sdl3_pong: CMakeFiles/sdl3_pong.dir/build.make
sdl3_pong: /usr/local/lib/libSDL3.0.dylib
sdl3_pong: CMakeFiles/sdl3_pong.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable sdl3_pong"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sdl3_pong.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sdl3_pong.dir/build: sdl3_pong
.PHONY : CMakeFiles/sdl3_pong.dir/build

CMakeFiles/sdl3_pong.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sdl3_pong.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sdl3_pong.dir/clean

CMakeFiles/sdl3_pong.dir/depend:
	cd /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/CMakeFiles/sdl3_pong.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/sdl3_pong.dir/depend

