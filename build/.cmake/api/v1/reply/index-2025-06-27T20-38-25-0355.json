{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/bin/cmake", "cpack": "/opt/homebrew/bin/cpack", "ctest": "/opt/homebrew/bin/ctest", "root": "/opt/homebrew/share/cmake"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 1, "string": "4.0.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-2888e44317c8b2830ed9.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-b4118e788428a0bbaa11.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b449c15c158dededd6bf.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-e7b97717190da4c202e2.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-b4118e788428a0bbaa11.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-2888e44317c8b2830ed9.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-e7b97717190da4c202e2.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b449c15c158dededd6bf.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}