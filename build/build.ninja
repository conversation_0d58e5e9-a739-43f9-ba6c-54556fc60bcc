# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sdl3_pong
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/
# =============================================================================
# Object build statements for EXECUTABLE target sdl3_pong


#############################################
# Order-only phony target for sdl3_pong

build cmake_object_order_depends_target_sdl3_pong: phony || .

build CMakeFiles/sdl3_pong.dir/learn/ECS/ECS.c.o: C_COMPILER__sdl3_pong_unscanned_Debug /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/learn/ECS/ECS.c || cmake_object_order_depends_target_sdl3_pong
  CONFIG = Debug
  DEP_FILE = CMakeFiles/sdl3_pong.dir/learn/ECS/ECS.c.o.d
  FLAGS = -g -std=gnu99 -arch arm64
  INCLUDES = -I/opt/homebrew/include
  OBJECT_DIR = CMakeFiles/sdl3_pong.dir
  OBJECT_FILE_DIR = CMakeFiles/sdl3_pong.dir/learn/ECS

build CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o: C_COMPILER__sdl3_pong_unscanned_Debug /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/learn/ECS/testECS.c || cmake_object_order_depends_target_sdl3_pong
  CONFIG = Debug
  DEP_FILE = CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o.d
  FLAGS = -g -std=gnu99 -arch arm64
  INCLUDES = -I/opt/homebrew/include
  OBJECT_DIR = CMakeFiles/sdl3_pong.dir
  OBJECT_FILE_DIR = CMakeFiles/sdl3_pong.dir/learn/ECS


# =============================================================================
# Link build statements for EXECUTABLE target sdl3_pong


#############################################
# Link the executable sdl3_pong

build sdl3_pong: C_EXECUTABLE_LINKER__sdl3_pong_Debug CMakeFiles/sdl3_pong.dir/learn/ECS/ECS.c.o CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o | /opt/homebrew/lib/libSDL3.0.dylib
  CONFIG = Debug
  FLAGS = -g -arch arm64
  LINK_LIBRARIES = -Wl,-rpath,/opt/homebrew/lib  /opt/homebrew/lib/libSDL3.0.dylib
  OBJECT_DIR = CMakeFiles/sdl3_pong.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = sdl3_pong
  TARGET_PDB = sdl3_pong.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build && /opt/homebrew/bin/ccmake -S/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice -B/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice -B/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build

build all: phony sdl3_pong

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build/cmake_install.cmake: RERUN_CMAKE | /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/CMakeLists.txt /opt/homebrew/lib/cmake/SDL3/SDL3Config.cmake /opt/homebrew/lib/cmake/SDL3/SDL3ConfigVersion.cmake /opt/homebrew/lib/cmake/SDL3/SDL3headersTargets.cmake /opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets-release.cmake /opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets.cmake /opt/homebrew/lib/cmake/SDL3/SDL3testTargets-release.cmake /opt/homebrew/lib/cmake/SDL3/SDL3testTargets.cmake /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/share/cmake/Modules/FeatureSummary.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.1/CMakeCCompiler.cmake CMakeFiles/4.0.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/CMakeLists.txt /opt/homebrew/lib/cmake/SDL3/SDL3Config.cmake /opt/homebrew/lib/cmake/SDL3/SDL3ConfigVersion.cmake /opt/homebrew/lib/cmake/SDL3/SDL3headersTargets.cmake /opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets-release.cmake /opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets.cmake /opt/homebrew/lib/cmake/SDL3/SDL3testTargets-release.cmake /opt/homebrew/lib/cmake/SDL3/SDL3testTargets.cmake /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/share/cmake/Modules/FeatureSummary.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.0.1/CMakeCCompiler.cmake CMakeFiles/4.0.1/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
