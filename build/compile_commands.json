[{"directory": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build", "command": "/usr/bin/clang -I/opt/homebrew/include -g -std=gnu99 -arch arm64 -o CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o -c /Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/sdl3_pong_GPU.c", "file": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/sdl3_pong_GPU.c", "output": "CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o"}]