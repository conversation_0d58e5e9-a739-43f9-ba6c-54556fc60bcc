# ninja log v6
2	214	1751057998438705088	CMakeFiles/sdl3_pong.dir/sdl3_pong_GPU.c.o	e09459bae3a37655
107	246	1751197587766562812	sdl3_pong	5e0cffdb1a6bf1a
1	292	1751135696412599781	CMakeFiles/sdl3_pong.dir/learn/GPUBasic/basic6_TexturedQuad.c.o	7d0fdb0b4b846535
0	237	1751159151304056362	CMakeFiles/sdl3_pong.dir/learn/pong/sdl3_pong_GPU.c.o	decaf536a8536ac0
0	107	1751197587659851485	CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o	26109ab10727c8aa
1	141	1751202198613871284	CMakeFiles/sdl3_pong.dir/learn/ECS/ECS.c.o	4958f4cdb0965a0a
1	142	1751202198614228332	CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o	26109ab10727c8aa
142	275	1751202198755119670	sdl3_pong	3946d66e08fde064
6	57	1751206746523005427	CMakeFiles/sdl3_pong.dir/learn/ECS/ECS.c.o	4958f4cdb0965a0a
6	58	1751206746523339079	CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o	26109ab10727c8aa
58	111	1751206746574600834	sdl3_pong	3946d66e08fde064
1	50	1751215121468486899	CMakeFiles/sdl3_pong.dir/learn/ECS/ECS.c.o	4958f4cdb0965a0a
2	52	1751215121468802485	CMakeFiles/sdl3_pong.dir/learn/ECS/testECS.c.o	26109ab10727c8aa
52	100	1751215121519127380	sdl3_pong	3946d66e08fde064
