cmake_minimum_required(VERSION 3.16)
project(sdl3_pong C)
set(CMAKE_C_STANDARD 99)

find_package(SDL3 REQUIRED CONFIG)

# Define source files
set(SOURCES
    # learn/pong/sdl3_pong_default_renderer.c  # Comment out to build GPU version
    # learn/GPUBasic/basic6_TexturedQuad.c
    # learn/pong/sdl3_pong_GPU.c
    learn/ECS/ECS.c
    learn/ECS/testECS.c
)

add_executable(sdl3_pong ${SOURCES})
target_link_libraries(sdl3_pong PRIVATE SDL3::SDL3)
