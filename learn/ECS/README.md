# ECS (Entity Component System) Implementation in C

A complete Entity Component System implementation in C, designed to mirror modern C++ ECS patterns while maintaining C's simplicity and performance characteristics.

## Overview

This ECS implementation provides a data-oriented approach to game entity management, separating data (components) from behavior (systems). It features efficient memory pooling, fast entity lookup, and a clean API for component management.

## Architecture

### Core Concepts

- **Entity**: A unique identifier with a tag for logical grouping
- **Component**: Pure data structures (Transform, Name, Shape, Box, Text)
- **EntityManager**: Manages entity lifecycle and component allocation

### Memory Management

- **Component Pooling**: Pre-allocated arrays for each component type
- **Free List Management**: Efficient component allocation/deallocation
- **Automatic Cleanup**: Dead entity removal with component recycling

## Components

| Component | Description | Data |
|-----------|-------------|------|
| `CTransform` | Position, rotation, scale | x, y, angle, scale_x, scale_y |
| `CName` | String identifier | name[64] |
| `CShape` | Shape type identifier | type[256] |
| `CCircle` | Circular geometry (includes CShape) | shape, radius, sides, color (r,g,b,a) |
| `CRectangle` | Rectangular geometry (includes CShape) | shape, width, height, color (r,g,b,a) |
| `CBoundingBox` | Collision detection bounds | x, y, width, height |
| `CText` | Text rendering | text[256], font_size, color (r,g,b,a) |

## API Reference

### EntityManager

```c
// Create and destroy entity manager
EntityManager* entity_manager_create(void);
void entity_manager_destroy(EntityManager* manager);

// Entity lifecycle
Entity* entity_manager_create_entity(EntityManager* manager, const char* tag);
void entity_manager_update(EntityManager* manager);  // Remove dead entities

// Entity lookup
Entity* entity_manager_get_entity_by_id(EntityManager* manager, size_t id);
Entity** entity_manager_get_entities_by_tag(EntityManager* manager, const char* tag, size_t* count);
```

### Entity Operations

```c
// Entity properties
const char* entity_get_tag(const Entity* entity);
size_t entity_get_id(const Entity* entity);
void entity_destroy(Entity* entity);

// Component management
void entity_add_transform(Entity* entity, float x, float y, float angle, float scale_x, float scale_y);
void entity_add_name(Entity* entity, const char* name);
void entity_add_shape(Entity* entity, const char* type);
void entity_add_bounding_box(Entity* entity, float x, float y, float width, float height);
void entity_add_text(Entity* entity, const char* text, int font_size, float r, float g, float b, float a);

// Component removal
void entity_remove_transform(Entity* entity);
void entity_remove_name(Entity* entity);
void entity_remove_shape(Entity* entity);
void entity_remove_bounding_box(Entity* entity);
void entity_remove_text(Entity* entity);

// Component queries
bool entity_has_transform(const Entity* entity);
bool entity_has_name(const Entity* entity);
bool entity_has_shape(const Entity* entity);
bool entity_has_bounding_box(const Entity* entity);
bool entity_has_text(const Entity* entity);
```

## Usage Example

```c
#include "ECS.h"

int main() {
    // Create entity manager
    EntityManager* manager = entity_manager_create();
    
    // Create game entities
    Entity* player = entity_manager_create_entity(manager, "player");
    Entity* enemy = entity_manager_create_entity(manager, "enemy");
    
    // Add components to player
    entity_add_transform(player, 400.0f, 300.0f, 0.0f, 1.0f, 1.0f);
    entity_add_name(player, "Hero");
    entity_add_shape(player, "rectangle");
    entity_add_bounding_box(player, 400.0f, 300.0f, 32.0f, 32.0f);

    // Add components to enemy
    entity_add_transform(enemy, 100.0f, 100.0f, 0.0f, 1.0f, 1.0f);
    entity_add_shape(enemy, "triangle");
    entity_add_bounding_box(enemy, 100.0f, 100.0f, 16.0f, 16.0f);
    
    // Game loop simulation
    if (entity_has_transform(player)) {
        printf("Player at: (%.2f, %.2f)\n", 
               player->c_transform->x, player->c_transform->y);
    }
    
    // Find all enemies
    size_t enemy_count;
    Entity** enemies = entity_manager_get_entities_by_tag(manager, "enemy", &enemy_count);
    printf("Found %zu enemies\n", enemy_count);
    free(enemies); // Remember to free the returned array
    
    // Destroy an entity
    entity_destroy(enemy);
    entity_manager_update(manager); // Clean up dead entities
    
    // Cleanup
    entity_manager_destroy(manager);
    return 0;
}
```

## Building and Testing

This ECS implementation is integrated with the project's CMake build system:

```bash
# Build the project
cmake --build build

# Run the ECS test suite
./build/sdl3_pong
```

## Test Suite

The implementation includes comprehensive tests covering:

- ✅ Entity creation and management
- ✅ Component attachment and removal  
- ✅ Entity lookup by ID and tag
- ✅ Entity destruction and cleanup
- ✅ Component pooling and memory management
- ✅ Real-world game scenario simulation

## Performance Characteristics

- **Entity Creation**: O(1) - Direct array access
- **Component Addition**: O(1) - Pool allocation with free list
- **Entity Lookup by ID**: O(n) - Linear search through active entities
- **Entity Lookup by Tag**: O(n) - Linear search with tag comparison
- **Memory Usage**: Fixed pools prevent fragmentation

## Design Decisions

### C-Style Implementation
- Uses function pointers and structs instead of classes
- Manual memory management with clear ownership
- Component pools for cache-friendly data access

### Component Storage
- Components stored in separate pools by type
- Entities hold pointers to their components
- Free list management for efficient allocation

### Entity Management
- Entities stored in a packed array
- Dead entities removed via array compaction
- Unique IDs prevent use-after-free issues

## Limitations

- **Maximum Entities**: 1000 (configurable via `MAX_ENTITIES`)
- **Component Strings**: Fixed-size buffers (name: 64 chars, text: 256 chars)
- **No Systems**: This is a pure ECS data layer (systems would be implemented separately)

## Future Enhancements

- Component serialization/deserialization
- Event system integration
- Multi-threaded component processing
- Dynamic component registration
- Sparse set optimization for component storage

---

*This ECS implementation demonstrates modern game architecture patterns in C, providing a solid foundation for data-oriented game development.*
