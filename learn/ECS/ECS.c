#include "ECS.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// Entity functions
const char* entity_get_tag(const Entity* entity) {
    if (!entity) return NULL;
    return entity->tag;
}

size_t entity_get_id(const Entity* entity) {
    if (!entity) return 0;
    return entity->id;
}

void entity_destroy(Entity* entity) {
    if (!entity) return;
    entity->alive = false;
}

// Helper function to get a free component from pool
static size_t get_free_component_index(size_t* free_indices, size_t* free_count, size_t max_count) {
    if (*free_count > 0) {
        (*free_count)--;
        return free_indices[*free_count];
    }

    // If no free indices, find the next available slot
    for (size_t i = 0; i < max_count; i++) {
        bool found = false;
        for (size_t j = 0; j < *free_count; j++) {
            if (free_indices[j] == i) {
                found = true;
                break;
            }
        }
        if (!found) {
            return i;
        }
    }

    return SIZE_MAX; // No free slots
}

// Helper function to return a component to the free pool
static void return_component_index(size_t* free_indices, size_t* free_count, size_t index, size_t max_count) {
    if (*free_count < max_count) {
        free_indices[*free_count] = index;
        (*free_count)++;
    }
}

// Component management functions
void entity_add_transform(Entity* entity, float x, float y, float angle, float scale_x, float scale_y) {
    if (!entity || !entity->manager) return;

    if (entity->c_transform) {
        // Update existing transform
        entity->c_transform->x = x;
        entity->c_transform->y = y;
        entity->c_transform->angle = angle;
        entity->c_transform->scale_x = scale_x;
        entity->c_transform->scale_y = scale_y;
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(manager->transform_free_indices,
                                          &manager->transform_free_count,
                                          MAX_ENTITIES);

    if (index == SIZE_MAX) return; // No free slots

    CTransform* transform = &manager->transform_pool[index];
    transform->x = x;
    transform->y = y;
    transform->angle = angle;
    transform->scale_x = scale_x;
    transform->scale_y = scale_y;

    entity->c_transform = transform;
}

void entity_add_name(Entity* entity, const char* name) {
    if (!entity || !entity->manager || !name) return;

    if (entity->c_name) {
        // Update existing name
        strncpy(entity->c_name->name, name, sizeof(entity->c_name->name) - 1);
        entity->c_name->name[sizeof(entity->c_name->name) - 1] = '\0';
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(manager->name_free_indices,
                                          &manager->name_free_count,
                                          MAX_ENTITIES);

    if (index == SIZE_MAX) return; // No free slots

    CName* name_comp = &manager->name_pool[index];
    strncpy(name_comp->name, name, sizeof(name_comp->name) - 1);
    name_comp->name[sizeof(name_comp->name) - 1] = '\0';

    entity->c_name = name_comp;
}

void entity_add_shape(Entity* entity, const char* type) {
    if (!entity || !entity->manager || !type) return;

    if (entity->c_shape) {
        // Update existing shape
        strncpy(entity->c_shape->type, type, sizeof(entity->c_shape->type) - 1);
        entity->c_shape->type[sizeof(entity->c_shape->type) - 1] = '\0';
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(manager->shape_free_indices,
                                          &manager->shape_free_count,
                                          MAX_ENTITIES);

    if (index == SIZE_MAX) return; // No free slots

    CShape* shape = &manager->shape_pool[index];
    strncpy(shape->type, type, sizeof(shape->type) - 1);
    shape->type[sizeof(shape->type) - 1] = '\0';

    entity->c_shape = shape;
}

void entity_add_bounding_box(Entity* entity, float x, float y, float width, float height) {
    if (!entity || !entity->manager) return;

    if (entity->c_bounding_box) {
        // Update existing bounding box
        entity->c_bounding_box->x = x;
        entity->c_bounding_box->y = y;
        entity->c_bounding_box->width = width;
        entity->c_bounding_box->height = height;
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(manager->bounding_box_free_indices,
                                          &manager->bounding_box_free_count,
                                          MAX_ENTITIES);

    if (index == SIZE_MAX) return; // No free slots

    CBoundingBox* bounding_box = &manager->bounding_box_pool[index];
    bounding_box->x = x;
    bounding_box->y = y;
    bounding_box->width = width;
    bounding_box->height = height;

    entity->c_bounding_box = bounding_box;
}



void entity_add_text(Entity* entity, const char* text, int font_size, float r, float g, float b, float a) {
    if (!entity || !entity->manager || !text) return;

    if (entity->c_text) {
        // Update existing text
        strncpy(entity->c_text->text, text, sizeof(entity->c_text->text) - 1);
        entity->c_text->text[sizeof(entity->c_text->text) - 1] = '\0';
        entity->c_text->font_size = font_size;
        entity->c_text->r = r;
        entity->c_text->g = g;
        entity->c_text->b = b;
        entity->c_text->a = a;
        return;
    }

    EntityManager* manager = entity->manager;
    size_t index = get_free_component_index(manager->text_free_indices,
                                          &manager->text_free_count,
                                          MAX_ENTITIES);

    if (index == SIZE_MAX) return; // No free slots

    CText* text_comp = &manager->text_pool[index];
    strncpy(text_comp->text, text, sizeof(text_comp->text) - 1);
    text_comp->text[sizeof(text_comp->text) - 1] = '\0';
    text_comp->font_size = font_size;
    text_comp->r = r;
    text_comp->g = g;
    text_comp->b = b;
    text_comp->a = a;

    entity->c_text = text_comp;
}

// Component removal functions
void entity_remove_transform(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_transform) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_transform - manager->transform_pool;
    return_component_index(manager->transform_free_indices,
                          &manager->transform_free_count,
                          index, MAX_ENTITIES);
    entity->c_transform = NULL;
}

void entity_remove_name(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_name) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_name - manager->name_pool;
    return_component_index(manager->name_free_indices,
                          &manager->name_free_count,
                          index, MAX_ENTITIES);
    entity->c_name = NULL;
}

void entity_remove_shape(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_shape) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_shape - manager->shape_pool;
    return_component_index(manager->shape_free_indices,
                          &manager->shape_free_count,
                          index, MAX_ENTITIES);
    entity->c_shape = NULL;
}

void entity_remove_bounding_box(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_bounding_box) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_bounding_box - manager->bounding_box_pool;
    return_component_index(manager->bounding_box_free_indices,
                          &manager->bounding_box_free_count,
                          index, MAX_ENTITIES);
    entity->c_bounding_box = NULL;
}



void entity_remove_text(Entity* entity) {
    if (!entity || !entity->manager || !entity->c_text) return;

    EntityManager* manager = entity->manager;
    size_t index = entity->c_text - manager->text_pool;
    return_component_index(manager->text_free_indices,
                          &manager->text_free_count,
                          index, MAX_ENTITIES);
    entity->c_text = NULL;
}

// Component check functions
bool entity_has_transform(const Entity* entity) {
    return entity && entity->c_transform != NULL;
}

bool entity_has_name(const Entity* entity) {
    return entity && entity->c_name != NULL;
}

bool entity_has_shape(const Entity* entity) {
    return entity && entity->c_shape != NULL;
}

bool entity_has_bounding_box(const Entity* entity) {
    return entity && entity->c_bounding_box != NULL;
}



bool entity_has_text(const Entity* entity) {
    return entity && entity->c_text != NULL;
}

// EntityManager functions
EntityManager* entity_manager_create(void) {
    EntityManager* manager = malloc(sizeof(EntityManager));
    if (!manager) return NULL;

    // Initialize entity array
    memset(manager->entities, 0, sizeof(manager->entities));
    manager->entity_count = 0;
    manager->next_id = 1; // Start IDs from 1, 0 is reserved for invalid

    // Initialize component pools
    memset(manager->transform_pool, 0, sizeof(manager->transform_pool));
    memset(manager->name_pool, 0, sizeof(manager->name_pool));
    memset(manager->shape_pool, 0, sizeof(manager->shape_pool));
    memset(manager->bounding_box_pool, 0, sizeof(manager->bounding_box_pool));
    memset(manager->text_pool, 0, sizeof(manager->text_pool));

    // Initialize free indices (all components are initially free)
    for (size_t i = 0; i < MAX_ENTITIES; i++) {
        manager->transform_free_indices[i] = i;
        manager->name_free_indices[i] = i;
        manager->shape_free_indices[i] = i;
        manager->bounding_box_free_indices[i] = i;
        manager->text_free_indices[i] = i;
    }

    manager->transform_free_count = MAX_ENTITIES;
    manager->name_free_count = MAX_ENTITIES;
    manager->shape_free_count = MAX_ENTITIES;
    manager->bounding_box_free_count = MAX_ENTITIES;
    manager->text_free_count = MAX_ENTITIES;

    return manager;
}

void entity_manager_destroy(EntityManager* manager) {
    if (!manager) return;

    // Clean up all entities (remove their components)
    for (size_t i = 0; i < manager->entity_count; i++) {
        Entity* entity = &manager->entities[i];
        entity_remove_transform(entity);
        entity_remove_name(entity);
        entity_remove_shape(entity);
        entity_remove_bounding_box(entity);
        entity_remove_text(entity);
    }

    free(manager);
}

Entity* entity_manager_create_entity(EntityManager* manager, const char* tag) {
    if (!manager || !tag || manager->entity_count >= MAX_ENTITIES) {
        return NULL;
    }

    Entity* entity = &manager->entities[manager->entity_count];
    entity->id = manager->next_id++;
    entity->alive = true;
    entity->manager = manager;

    // Copy tag
    strncpy(entity->tag, tag, sizeof(entity->tag) - 1);
    entity->tag[sizeof(entity->tag) - 1] = '\0';

    // Initialize component pointers to NULL
    entity->c_transform = NULL;
    entity->c_name = NULL;
    entity->c_shape = NULL;
    entity->c_bounding_box = NULL;
    entity->c_text = NULL;

    manager->entity_count++;
    return entity;
}

void entity_manager_update(EntityManager* manager) {
    if (!manager) return;

    // Remove dead entities by compacting the array
    size_t write_index = 0;
    for (size_t read_index = 0; read_index < manager->entity_count; read_index++) {
        Entity* entity = &manager->entities[read_index];

        if (entity->alive) {
            if (write_index != read_index) {
                // Move entity to fill the gap
                manager->entities[write_index] = *entity;
                // Update the manager pointer in the moved entity
                manager->entities[write_index].manager = manager;
            }
            write_index++;
        } else {
            // Entity is dead, remove its components
            entity_remove_transform(entity);
            entity_remove_name(entity);
            entity_remove_shape(entity);
            entity_remove_bounding_box(entity);
            entity_remove_text(entity);
        }
    }

    manager->entity_count = write_index;
}

Entity* entity_manager_get_entity_by_id(EntityManager* manager, size_t id) {
    if (!manager) return NULL;

    for (size_t i = 0; i < manager->entity_count; i++) {
        if (manager->entities[i].id == id && manager->entities[i].alive) {
            return &manager->entities[i];
        }
    }

    return NULL;
}

// Note: This function returns a dynamically allocated array that must be freed by the caller
Entity** entity_manager_get_entities_by_tag(EntityManager* manager, const char* tag, size_t* count) {
    if (!manager || !tag || !count) {
        if (count) *count = 0;
        return NULL;
    }

    // First pass: count matching entities
    size_t matching_count = 0;
    for (size_t i = 0; i < manager->entity_count; i++) {
        if (manager->entities[i].alive && strcmp(manager->entities[i].tag, tag) == 0) {
            matching_count++;
        }
    }

    *count = matching_count;
    if (matching_count == 0) {
        return NULL;
    }

    // Allocate array for entity pointers
    Entity** result = malloc(sizeof(Entity*) * matching_count);
    if (!result) {
        *count = 0;
        return NULL;
    }

    // Second pass: collect matching entities
    size_t result_index = 0;
    for (size_t i = 0; i < manager->entity_count; i++) {
        if (manager->entities[i].alive && strcmp(manager->entities[i].tag, tag) == 0) {
            result[result_index++] = &manager->entities[i];
        }
    }

    return result;
}

void entity_manager_print_entities(const EntityManager* manager) {
    if (!manager) {
        printf("EntityManager is NULL\n");
        return;
    }

    printf("=== EntityManager Status ===\n");
    printf("Total entities: %zu\n", manager->entity_count);
    printf("Next ID: %zu\n", manager->next_id);
    printf("\n");

    for (size_t i = 0; i < manager->entity_count; i++) {
        const Entity* entity = &manager->entities[i];
        printf("Entity[%zu]: ID=%zu, Tag='%s', Alive=%s\n",
               i, entity->id, entity->tag, entity->alive ? "true" : "false");

        // Print components
        if (entity->c_transform) {
            printf("  Transform: pos(%.2f, %.2f), angle=%.2f, scale(%.2f, %.2f)\n",
                   entity->c_transform->x, entity->c_transform->y, entity->c_transform->angle,
                   entity->c_transform->scale_x, entity->c_transform->scale_y);
        }

        if (entity->c_name) {
            printf("  Name: '%s'\n", entity->c_name->name);
        }

        if (entity->c_shape) {
            printf("  Shape: type='%s'\n", entity->c_shape->type);
        }

        if (entity->c_bounding_box) {
            printf("  BoundingBox: pos(%.2f, %.2f), size(%.2f, %.2f)\n",
                   entity->c_bounding_box->x, entity->c_bounding_box->y,
                   entity->c_bounding_box->width, entity->c_bounding_box->height);
        }

        if (entity->c_text) {
            printf("  Text: '%s', font_size=%d, color(%.2f, %.2f, %.2f, %.2f)\n",
                   entity->c_text->text, entity->c_text->font_size,
                   entity->c_text->r, entity->c_text->g, entity->c_text->b, entity->c_text->a);
        }

        printf("\n");
    }

    printf("=== Component Pool Status ===\n");
    printf("Transform free: %zu/%d\n", manager->transform_free_count, MAX_ENTITIES);
    printf("Name free: %zu/%d\n", manager->name_free_count, MAX_ENTITIES);
    printf("Shape free: %zu/%d\n", manager->shape_free_count, MAX_ENTITIES);
    printf("BoundingBox free: %zu/%d\n", manager->bounding_box_free_count, MAX_ENTITIES);
    printf("Text free: %zu/%d\n", manager->text_free_count, MAX_ENTITIES);
    printf("=============================\n\n");
}