#ifndef ECS_H
#define ECS_H

#include <stddef.h>
#include <stdbool.h>

// Forward declarations
typedef struct Entity Entity;
typedef struct EntityManager EntityManager;

// Component structures
typedef struct {
    float x, y;
    float angle;
    float scale_x, scale_y;
} CTransform;

typedef struct {
    char name[64];
} CName;

typedef struct {
    char type[256];
} CShape;

typedef struct {
    CShape shape;  // Must be first property
    float radius;
    int sides;
    float r, g, b, a;  // color components
} CCircle;

typedef struct {
    CShape shape;  // Must be first property
    float width, height;
    float r, g, b, a;  // color components
} CRectangle;

typedef struct {
    float x, y;      // position
    float width, height;  // size
} CBoundingBox;

typedef struct {
    char text[256];
    int font_size;
    float r, g, b, a;  // color components
} CText; //for display overlay text (debug)

// Entity structure
struct Entity {
    size_t id;
    char tag[32];
    bool alive;

    // Component pointers (NULL if component not present)
    CTransform* c_transform;
    CName* c_name;
    CShape* c_shape;
    CBoundingBox* c_bounding_box;
    CText* c_text;

    // Reference to the entity manager that owns this entity
    EntityManager* manager;
};

// Entity function declarations
const char* entity_get_tag(const Entity* entity);
size_t entity_get_id(const Entity* entity);
void entity_destroy(Entity* entity);

// Component management functions
void entity_add_transform(Entity* entity, float x, float y, float angle, float scale_x, float scale_y);
void entity_add_name(Entity* entity, const char* name);
void entity_add_shape(Entity* entity, const char* type);
void entity_add_bounding_box(Entity* entity, float x, float y, float width, float height);
void entity_add_text(Entity* entity, const char* text, int font_size, float r, float g, float b, float a);

void entity_remove_transform(Entity* entity);
void entity_remove_name(Entity* entity);
void entity_remove_shape(Entity* entity);
void entity_remove_bounding_box(Entity* entity);
void entity_remove_text(Entity* entity);

bool entity_has_transform(const Entity* entity);
bool entity_has_name(const Entity* entity);
bool entity_has_shape(const Entity* entity);
bool entity_has_bounding_box(const Entity* entity);
bool entity_has_text(const Entity* entity);

// EntityManager structure
#define MAX_ENTITIES 1000

struct EntityManager {
    Entity entities[MAX_ENTITIES];
    size_t entity_count;
    size_t next_id;

    // Component pools
    CTransform transform_pool[MAX_ENTITIES];
    CName name_pool[MAX_ENTITIES];
    CShape shape_pool[MAX_ENTITIES];
    CBoundingBox bounding_box_pool[MAX_ENTITIES];
    CText text_pool[MAX_ENTITIES];

    // Free component indices
    size_t transform_free_indices[MAX_ENTITIES];
    size_t name_free_indices[MAX_ENTITIES];
    size_t shape_free_indices[MAX_ENTITIES];
    size_t bounding_box_free_indices[MAX_ENTITIES];
    size_t text_free_indices[MAX_ENTITIES];

    size_t transform_free_count;
    size_t name_free_count;
    size_t shape_free_count;
    size_t bounding_box_free_count;
    size_t text_free_count;
};

// EntityManager function declarations
EntityManager* entity_manager_create(void);
void entity_manager_destroy(EntityManager* manager);

Entity* entity_manager_create_entity(EntityManager* manager, const char* tag);
void entity_manager_update(EntityManager* manager);  // Remove dead entities
Entity* entity_manager_get_entity_by_id(EntityManager* manager, size_t id);
Entity** entity_manager_get_entities_by_tag(EntityManager* manager, const char* tag, size_t* count);

// Utility functions
void entity_manager_print_entities(const EntityManager* manager);

#endif // ECS_H