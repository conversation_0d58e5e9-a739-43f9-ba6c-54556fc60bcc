#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include <string.h>
#include "ECS.h"

void test_entity_manager_creation() {
    printf("Testing EntityManager creation...\n");

    EntityManager* manager = entity_manager_create();
    assert(manager != NULL);
    assert(manager->entity_count == 0);
    assert(manager->next_id == 1);

    entity_manager_destroy(manager);
    printf("✓ EntityManager creation test passed\n\n");
}

void test_entity_creation() {
    printf("Testing Entity creation...\n");

    EntityManager* manager = entity_manager_create();
    assert(manager != NULL);

    // Create entities
    Entity* player = entity_manager_create_entity(manager, "player");
    Entity* enemy = entity_manager_create_entity(manager, "enemy");
    Entity* bullet = entity_manager_create_entity(manager, "bullet");

    assert(player != NULL);
    assert(enemy != NULL);
    assert(bullet != NULL);

    // Check entity properties
    assert(entity_get_id(player) == 1);
    assert(entity_get_id(enemy) == 2);
    assert(entity_get_id(bullet) == 3);

    assert(strcmp(entity_get_tag(player), "player") == 0);
    assert(strcmp(entity_get_tag(enemy), "enemy") == 0);
    assert(strcmp(entity_get_tag(bullet), "bullet") == 0);

    assert(player->alive == true);
    assert(enemy->alive == true);
    assert(bullet->alive == true);

    assert(manager->entity_count == 3);

    entity_manager_destroy(manager);
    printf("✓ Entity creation test passed\n\n");
}

void test_component_management() {
    printf("Testing Component management...\n");

    EntityManager* manager = entity_manager_create();
    Entity* entity = entity_manager_create_entity(manager, "test");

    // Test Transform component
    assert(!entity_has_transform(entity));
    entity_add_transform(entity, 100.0f, 200.0f, 45.0f, 1.5f, 2.0f);
    assert(entity_has_transform(entity));
    assert(entity->c_transform->x == 100.0f);
    assert(entity->c_transform->y == 200.0f);
    assert(entity->c_transform->angle == 45.0f);
    assert(entity->c_transform->scale_x == 1.5f);
    assert(entity->c_transform->scale_y == 2.0f);

    // Test Name component
    assert(!entity_has_name(entity));
    entity_add_name(entity, "TestEntity");
    assert(entity_has_name(entity));
    assert(strcmp(entity->c_name->name, "TestEntity") == 0);

    // Test Shape component
    assert(!entity_has_shape(entity));
    entity_add_shape(entity, 25.0f, 6, 1.0f, 0.5f, 0.0f, 1.0f);
    assert(entity_has_shape(entity));
    assert(entity->c_shape->radius == 25.0f);
    assert(entity->c_shape->sides == 6);
    assert(entity->c_shape->r == 1.0f);
    assert(entity->c_shape->g == 0.5f);
    assert(entity->c_shape->b == 0.0f);
    assert(entity->c_shape->a == 1.0f);

    // Test Box component
    assert(!entity_has_box(entity));
    entity_add_box(entity, 50.0f, 30.0f, 0.0f, 1.0f, 0.0f, 0.8f);
    assert(entity_has_box(entity));
    assert(entity->c_box->width == 50.0f);
    assert(entity->c_box->height == 30.0f);
    assert(entity->c_box->r == 0.0f);
    assert(entity->c_box->g == 1.0f);
    assert(entity->c_box->b == 0.0f);
    assert(entity->c_box->a == 0.8f);

    // Test Text component
    assert(!entity_has_text(entity));
    entity_add_text(entity, "Hello World!", 24, 1.0f, 1.0f, 1.0f, 1.0f);
    assert(entity_has_text(entity));
    assert(strcmp(entity->c_text->text, "Hello World!") == 0);
    assert(entity->c_text->font_size == 24);
    assert(entity->c_text->r == 1.0f);
    assert(entity->c_text->g == 1.0f);
    assert(entity->c_text->b == 1.0f);
    assert(entity->c_text->a == 1.0f);

    entity_manager_destroy(manager);
    printf("✓ Component management test passed\n\n");
}

void test_component_removal() {
    printf("Testing Component removal...\n");

    EntityManager* manager = entity_manager_create();
    Entity* entity = entity_manager_create_entity(manager, "test");

    // Add all components
    entity_add_transform(entity, 10.0f, 20.0f, 0.0f, 1.0f, 1.0f);
    entity_add_name(entity, "Test");
    entity_add_shape(entity, 15.0f, 4, 1.0f, 0.0f, 0.0f, 1.0f);
    entity_add_box(entity, 40.0f, 40.0f, 0.0f, 0.0f, 1.0f, 1.0f);
    entity_add_text(entity, "Test Text", 16, 0.0f, 1.0f, 0.0f, 1.0f);

    // Verify all components exist
    assert(entity_has_transform(entity));
    assert(entity_has_name(entity));
    assert(entity_has_shape(entity));
    assert(entity_has_box(entity));
    assert(entity_has_text(entity));

    // Remove components one by one
    entity_remove_transform(entity);
    assert(!entity_has_transform(entity));
    assert(entity_has_name(entity)); // Others should still exist

    entity_remove_name(entity);
    assert(!entity_has_name(entity));
    assert(entity_has_shape(entity)); // Others should still exist

    entity_remove_shape(entity);
    assert(!entity_has_shape(entity));
    assert(entity_has_box(entity)); // Others should still exist

    entity_remove_box(entity);
    assert(!entity_has_box(entity));
    assert(entity_has_text(entity)); // Text should still exist

    entity_remove_text(entity);
    assert(!entity_has_text(entity));

    entity_manager_destroy(manager);
    printf("✓ Component removal test passed\n\n");
}

void test_entity_destruction() {
    printf("Testing Entity destruction...\n");

    EntityManager* manager = entity_manager_create();

    // Create entities
    Entity* entity1 = entity_manager_create_entity(manager, "test1");
    Entity* entity2 = entity_manager_create_entity(manager, "test2");
    Entity* entity3 = entity_manager_create_entity(manager, "test3");

    // Add components to entities
    entity_add_transform(entity1, 10.0f, 10.0f, 0.0f, 1.0f, 1.0f);
    entity_add_name(entity2, "Entity2");
    entity_add_shape(entity3, 20.0f, 5, 1.0f, 0.0f, 0.0f, 1.0f);

    assert(manager->entity_count == 3);

    // Destroy middle entity
    entity_destroy(entity2);
    assert(entity2->alive == false);

    // Update manager to remove dead entities
    entity_manager_update(manager);
    assert(manager->entity_count == 2);

    // Verify remaining entities are still valid
    assert(entity1->alive == true);
    assert(entity3->alive == true);
    assert(entity_has_transform(entity1));
    assert(entity_has_shape(entity3));

    entity_manager_destroy(manager);
    printf("✓ Entity destruction test passed\n\n");
}

void test_entity_lookup() {
    printf("Testing Entity lookup...\n");

    EntityManager* manager = entity_manager_create();

    // Create entities with different tags
    Entity* player1 = entity_manager_create_entity(manager, "player");
    Entity* player2 = entity_manager_create_entity(manager, "player");
    Entity* enemy1 = entity_manager_create_entity(manager, "enemy");
    Entity* enemy2 = entity_manager_create_entity(manager, "enemy");
    Entity* bullet = entity_manager_create_entity(manager, "bullet");

    // Test lookup by ID
    Entity* found = entity_manager_get_entity_by_id(manager, entity_get_id(player1));
    assert(found == player1);

    found = entity_manager_get_entity_by_id(manager, entity_get_id(enemy2));
    assert(found == enemy2);

    found = entity_manager_get_entity_by_id(manager, 999); // Non-existent ID
    assert(found == NULL);

    // Test lookup by tag
    size_t count;
    Entity** players = entity_manager_get_entities_by_tag(manager, "player", &count);
    assert(players != NULL);
    assert(count == 2);
    assert((players[0] == player1 && players[1] == player2) ||
           (players[0] == player2 && players[1] == player1));
    free(players);

    Entity** enemies = entity_manager_get_entities_by_tag(manager, "enemy", &count);
    assert(enemies != NULL);
    assert(count == 2);
    free(enemies);

    Entity** bullets = entity_manager_get_entities_by_tag(manager, "bullet", &count);
    assert(bullets != NULL);
    assert(count == 1);
    assert(bullets[0] == bullet);
    free(bullets);

    Entity** nonexistent = entity_manager_get_entities_by_tag(manager, "nonexistent", &count);
    assert(nonexistent == NULL);
    assert(count == 0);

    entity_manager_destroy(manager);
    printf("✓ Entity lookup test passed\n\n");
}

void test_component_update() {
    printf("Testing Component update...\n");

    EntityManager* manager = entity_manager_create();
    Entity* entity = entity_manager_create_entity(manager, "test");

    // Add transform component
    entity_add_transform(entity, 0.0f, 0.0f, 0.0f, 1.0f, 1.0f);
    assert(entity->c_transform->x == 0.0f);

    // Update transform component (should modify existing, not create new)
    entity_add_transform(entity, 100.0f, 200.0f, 90.0f, 2.0f, 3.0f);
    assert(entity->c_transform->x == 100.0f);
    assert(entity->c_transform->y == 200.0f);
    assert(entity->c_transform->angle == 90.0f);
    assert(entity->c_transform->scale_x == 2.0f);
    assert(entity->c_transform->scale_y == 3.0f);

    // Add name component
    entity_add_name(entity, "Original");
    assert(strcmp(entity->c_name->name, "Original") == 0);

    // Update name component
    entity_add_name(entity, "Updated");
    assert(strcmp(entity->c_name->name, "Updated") == 0);

    entity_manager_destroy(manager);
    printf("✓ Component update test passed\n\n");
}

void test_comprehensive_scenario() {
    printf("Testing comprehensive scenario...\n");

    EntityManager* manager = entity_manager_create();

    // Create a game-like scenario
    Entity* player = entity_manager_create_entity(manager, "player");
    entity_add_transform(player, 400.0f, 300.0f, 0.0f, 1.0f, 1.0f);
    entity_add_name(player, "Hero");
    entity_add_box(player, 32.0f, 32.0f, 0.0f, 1.0f, 0.0f, 1.0f);

    // Create enemies
    for (int i = 0; i < 5; i++) {
        Entity* enemy = entity_manager_create_entity(manager, "enemy");
        entity_add_transform(enemy, 100.0f + i * 50.0f, 100.0f, 0.0f, 1.0f, 1.0f);
        entity_add_shape(enemy, 16.0f, 3, 1.0f, 0.0f, 0.0f, 1.0f);
    }

    // Create UI elements
    Entity* score_text = entity_manager_create_entity(manager, "ui");
    entity_add_transform(score_text, 10.0f, 10.0f, 0.0f, 1.0f, 1.0f);
    entity_add_text(score_text, "Score: 0", 24, 1.0f, 1.0f, 1.0f, 1.0f);

    assert(manager->entity_count == 7); // 1 player + 5 enemies + 1 UI

    // Print current state
    printf("Initial state:\n");
    entity_manager_print_entities(manager);

    // Simulate destroying some enemies
    size_t enemy_count;
    Entity** enemies = entity_manager_get_entities_by_tag(manager, "enemy", &enemy_count);
    assert(enemy_count == 5);

    // Destroy first two enemies
    entity_destroy(enemies[0]);
    entity_destroy(enemies[1]);
    free(enemies);

    // Update to remove dead entities
    entity_manager_update(manager);
    assert(manager->entity_count == 5); // 1 player + 3 enemies + 1 UI

    // Verify remaining enemies
    enemies = entity_manager_get_entities_by_tag(manager, "enemy", &enemy_count);
    assert(enemy_count == 3);
    free(enemies);

    printf("After destroying 2 enemies:\n");
    entity_manager_print_entities(manager);

    entity_manager_destroy(manager);
    printf("✓ Comprehensive scenario test passed\n\n");
}

int main(int argc, char *argv[]) {
    printf("=== ECS (Entity Component System) Test Suite ===\n\n");

    // Run all tests
    test_entity_manager_creation();
    test_entity_creation();
    test_component_management();
    test_component_removal();
    test_entity_destruction();
    test_entity_lookup();
    test_component_update();
    test_comprehensive_scenario();

    printf("🎉 All ECS tests passed successfully!\n");
    printf("\nECS Features Demonstrated:\n");
    printf("- Entity creation and management\n");
    printf("- Component attachment and removal\n");
    printf("- Entity lookup by ID and tag\n");
    printf("- Entity destruction and cleanup\n");
    printf("- Component pooling and memory management\n");
    printf("- Real-world game scenario simulation\n");

    return 0;
}
