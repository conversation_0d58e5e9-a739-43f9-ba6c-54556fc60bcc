name: 'release'
run-name: 'Create SDL release artifacts for ${{ inputs.commit }}'

on:
  workflow_dispatch:
    inputs:
      commit:
        description: 'Commit of SDL'
        required: true

jobs:

  src:
    runs-on: ubuntu-latest
    outputs:
      project: ${{ steps.releaser.outputs.project }}
      version: ${{ steps.releaser.outputs.version }}
      src-tar-gz: ${{ steps.releaser.outputs.src-tar-gz }}
      src-tar-xz: ${{ steps.releaser.outputs.src-tar-xz }}
      src-zip: ${{ steps.releaser.outputs.src-zip }}
    steps:
      - name: 'Set up Python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: 'Fetch build-release.py'
        uses: actions/checkout@v4
        with:
          sparse-checkout: 'build-scripts/build-release.py'
      - name: 'Set up SDL sources'
        uses: actions/checkout@v4
        with:
          path: 'SDL'
          fetch-depth: 0
      - name: 'Build Source archive'
        id: releaser
        shell: bash
        run: |
          python build-scripts/build-release.py \
            --actions source \
            --commit ${{ inputs.commit }} \
            --root "${{ github.workspace }}/SDL" \
            --github \
            --debug
      - name: 'Store source archives'
        uses: actions/upload-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace}}/dist'
      - name: 'Generate summary'
        run: |
          echo "Run the following commands to download all artifacts:" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "mkdir -p /tmp/${{ steps.releaser.outputs.project }}-${{ steps.releaser.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "cd /tmp/${{ steps.releaser.outputs.project }}-${{ steps.releaser.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "gh run -R ${{ github.repository }} download ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY

  linux-verify:
    needs: [src]
    runs-on: ubuntu-latest
    steps:
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Unzip ${{ needs.src.outputs.src-zip }}'
        id: zip
        run: |
          mkdir /tmp/zipdir
          cd /tmp/zipdir
          unzip "${{ github.workspace }}/${{ needs.src.outputs.src-zip }}"
          echo "path=/tmp/zipdir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: tar
        run: |
          mkdir -p /tmp/tardir
          tar -C /tmp/tardir -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=/tmp/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Compare contents of ${{ needs.src.outputs.src-zip }} and  ${{ needs.src.outputs.src-tar-gz }}'
        run: |
          diff /tmp/zipdir /tmp/tardir
      - name: 'Test versioning'
        shell: bash
        run: |
          ${{ steps.tar.outputs.path }}/build-scripts/test-versioning.sh
      - name: 'Install Linux dependencies'
        run: |
          sudo apt-get update -y
          sudo apt-get install -y \
            gnome-desktop-testing libasound2-dev libpulse-dev libaudio-dev libjack-dev libsndio-dev \
            libusb-1.0-0-dev libx11-dev libxext-dev libxrandr-dev libxcursor-dev libxfixes-dev libxi-dev \
            libxss-dev libwayland-dev libxkbcommon-dev libdrm-dev libgbm-dev libgl1-mesa-dev libgles2-mesa-dev \
            libegl1-mesa-dev libdbus-1-dev libibus-1.0-dev libudev-dev fcitx-libs-dev
      - name: 'CMake (configure + build + tests + examples)'
        run: |
          cmake -S ${{ steps.tar.outputs.path }} -B /tmp/build -DSDL_TEST_LIBRARY=TRUE -DSDL_TESTS=TRUE -DSDL_EXAMPLES=TRUE
          cmake --build /tmp/build --verbose
          ctest --test-dir /tmp/build --no-tests=error --output-on-failure

  dmg:
    needs: [src]
    runs-on: macos-latest
    outputs:
      dmg: ${{ steps.releaser.outputs.dmg }}
    steps:
      - name: 'Set up Python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: 'Fetch build-release.py'
        uses: actions/checkout@v4
        with:
          sparse-checkout: 'build-scripts/build-release.py'
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: tar
        run: |
          mkdir -p "${{ github.workspace }}/tardir"
          tar -C "${{ github.workspace }}/tardir" -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=${{ github.workspace }}/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Build SDL3.dmg'
        id: releaser
        shell: bash
        run: |
          python build-scripts/build-release.py \
            --actions dmg \
            --commit ${{ inputs.commit }} \
            --root "${{ steps.tar.outputs.path }}" \
            --github \
            --debug
      - name: 'Store DMG image file'
        uses: actions/upload-artifact@v4
        with:
          name: dmg
          path: '${{ github.workspace }}/dist'

  dmg-verify:
    needs: [dmg, src]
    runs-on: macos-latest
    steps:
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Download ${{ needs.dmg.outputs.dmg }}'
        uses: actions/download-artifact@v4
        with:
          name: dmg
          path: '${{ github.workspace }}'
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: src
        run: |
          mkdir -p /tmp/tardir
          tar -C /tmp/tardir -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=/tmp/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Mount ${{ needs.dmg.outputs.dmg }}'
        id: mount
        run: |
          hdiutil attach '${{ github.workspace }}/${{ needs.dmg.outputs.dmg }}'
          mount_point="/Volumes/${{ needs.src.outputs.project }}"
          if [ ! -d "$mount_point/${{ needs.src.outputs.project }}.xcframework" ]; then
            echo "Cannot find ${{ needs.src.outputs.project }}.xcframework!"
            exit 1
          fi
          echo "mount_point=$mount_point">>$GITHUB_OUTPUT
      - name: 'CMake (configure + build) Darwin'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"               \
              -DTEST_FULL=FALSE                                             \
              -DTEST_STATIC=FALSE                                           \
              -DTEST_TEST=FALSE                                             \
              -DCMAKE_PREFIX_PATH="${{ steps.mount.outputs.mount_point }}"  \
              -DCMAKE_SYSTEM_NAME=Darwin                                    \
              -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64"                      \
              -DCMAKE_OSX_DEPLOYMENT_TARGET=10.13                           \
              -Werror=dev                                                   \
              -B build_darwin
          cmake --build build_darwin --config Release --verbose

          cmake -S "${{ steps.src.outputs.path }}/cmake/test"                                                   \
              -DTEST_FULL=FALSE                                                                                 \
              -DTEST_STATIC=FALSE                                                                               \
              -DTEST_TEST=FALSE                                                                                 \
              -DCMAKE_PREFIX_PATH="${{ steps.mount.outputs.mount_point }}/SDL3.xcframework/macos-arm64_x86_64"  \
              -DCMAKE_SYSTEM_NAME=Darwin                                                                        \
              -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64"                                                          \
              -DCMAKE_OSX_DEPLOYMENT_TARGET=10.13                                                               \
              -Werror=dev                                                                                       \
              -B build_darwin_2
          cmake --build build_darwin --config Release --verbose
      - name: 'CMake (configure + build) iOS'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"               \
              -DTEST_FULL=FALSE                                             \
              -DTEST_STATIC=FALSE                                           \
              -DTEST_TEST=FALSE                                             \
              -DCMAKE_PREFIX_PATH="${{ steps.mount.outputs.mount_point }}"  \
              -DCMAKE_SYSTEM_NAME=iOS                                       \
              -DCMAKE_OSX_ARCHITECTURES="arm64"                             \
              -DCMAKE_OSX_DEPLOYMENT_TARGET=11.0                             \
              -Werror=dev                                                   \
              -B build_ios
          cmake --build build_ios --config Release --verbose
      - name: 'CMake (configure + build) tvOS'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"               \
              -DTEST_FULL=FALSE                                             \
              -DTEST_STATIC=FALSE                                           \
              -DTEST_TEST=FALSE                                             \
              -DCMAKE_PREFIX_PATH="${{ steps.mount.outputs.mount_point }}"  \
              -DCMAKE_SYSTEM_NAME=tvOS                                      \
              -DCMAKE_OSX_ARCHITECTURES="arm64"                             \
              -DCMAKE_OSX_DEPLOYMENT_TARGET=11.0                             \
              -Werror=dev                                                   \
              -B build_tvos
          cmake --build build_tvos --config Release --verbose
      - name: 'CMake (configure + build) iOS simulator'
        run: |
          sysroot=$(xcodebuild -version -sdk iphonesimulator Path)
          echo "sysroot=$sysroot"
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"               \
              -DTEST_FULL=FALSE                                             \
              -DTEST_STATIC=FALSE                                           \
              -DTEST_TEST=FALSE                                             \
              -DCMAKE_PREFIX_PATH="${{ steps.mount.outputs.mount_point }}"  \
              -DCMAKE_SYSTEM_NAME=iOS                                       \
              -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64"                      \
              -DCMAKE_OSX_SYSROOT="${sysroot}"                              \
              -DCMAKE_OSX_DEPLOYMENT_TARGET=11.0                            \
              -Werror=dev                                                   \
              -B build_ios_simulator
          cmake --build build_ios_simulator --config Release --verbose
      - name: 'CMake (configure + build) tvOS simulator'
        run: |
          sysroot=$(xcodebuild -version -sdk appletvsimulator Path)
          echo "sysroot=$sysroot"
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"               \
              -DTEST_FULL=FALSE                                             \
              -DTEST_STATIC=FALSE                                           \
              -DTEST_TEST=FALSE                                             \
              -DCMAKE_PREFIX_PATH="${{ steps.mount.outputs.mount_point }}"  \
              -DCMAKE_SYSTEM_NAME=tvOS                                      \
              -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64"                      \
              -DCMAKE_OSX_SYSROOT="${sysroot}"                              \
              -DCMAKE_OSX_DEPLOYMENT_TARGET=11.0                            \
              -Werror=dev                                                   \
              -B build_tvos_simulator
          cmake --build build_tvos_simulator --config Release --verbose

  msvc:
    needs: [src]
    runs-on: windows-2019
    outputs:
      VC-x86: ${{ steps.releaser.outputs.VC-x86 }}
      VC-x64: ${{ steps.releaser.outputs.VC-x64 }}
      VC-arm64: ${{ steps.releaser.outputs.VC-arm64 }}
      VC-devel: ${{ steps.releaser.outputs.VC-devel }}
    steps:
      - name: 'Set up Python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: 'Fetch build-release.py'
        uses: actions/checkout@v4
        with:
          sparse-checkout: 'build-scripts/build-release.py'
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Unzip ${{ needs.src.outputs.src-zip }}'
        id: zip
        run: |
          New-Item C:\temp -ItemType Directory -ErrorAction SilentlyContinue
          cd C:\temp
          unzip "${{ github.workspace }}/${{ needs.src.outputs.src-zip }}"
          echo "path=C:\temp\${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$Env:GITHUB_OUTPUT
      - name: 'Build MSVC binary archives'
        id: releaser
        run: |
          python build-scripts/build-release.py `
            --actions msvc `
            --commit ${{ inputs.commit }} `
            --root "${{ steps.zip.outputs.path }}" `
            --github `
            --debug
      - name: 'Store MSVC archives'
        uses: actions/upload-artifact@v4
        with:
          name: win32
          path: '${{ github.workspace }}/dist'

  msvc-verify:
    needs: [msvc, src]
    runs-on: windows-latest
    steps:
      - name: 'Fetch .github/actions/setup-ninja/action.yml'
        uses: actions/checkout@v4
        with:
          sparse-checkout: '.github/actions/setup-ninja/action.yml'
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Download MSVC binaries'
        uses: actions/download-artifact@v4
        with:
          name: win32
          path: '${{ github.workspace }}'
      - name: 'Unzip ${{ needs.src.outputs.src-zip }}'
        id: src
        run: |
          mkdir '${{ github.workspace }}/sources'
          cd '${{ github.workspace }}/sources'
          unzip "${{ github.workspace }}/${{ needs.src.outputs.src-zip }}"
          echo "path=${{ github.workspace }}/sources/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$env:GITHUB_OUTPUT
      - name: 'Unzip ${{ needs.msvc.outputs.VC-devel }}'
        id: bin
        run: |
          mkdir '${{ github.workspace }}/vc'
          cd '${{ github.workspace }}/vc'
          unzip "${{ github.workspace }}/${{ needs.msvc.outputs.VC-devel }}"
          echo "path=${{ github.workspace }}/vc/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$env:GITHUB_OUTPUT
      - name: Set up ninja
        uses: ./.github/actions/setup-ninja
      - name: 'Configure vcvars x86'
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: x64_x86
      - name: 'CMake (configure + build + tests) x86'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"     `
              -B build_x86                                        `
              -GNinja                                             `
              -DCMAKE_BUILD_TYPE=Debug                            `
              -Werror=dev                                         `
              -DTEST_FULL=TRUE                                    `
              -DTEST_STATIC=FALSE                                 `
              -DTEST_SHARED=TRUE                                  `
              -DTEST_TEST=TRUE                                    `
              -DCMAKE_SUPPRESS_REGENERATION=TRUE                  `
              -DCMAKE_PREFIX_PATH="${{ steps.bin.outputs.path }}"
          Start-Sleep -Seconds 2
          cmake --build build_x86 --config Release --verbose
          ctest --test-dir build_x86 --no-tests=error -C Release --output-on-failure
      - name: 'Configure vcvars x64'
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: x64
      - name: 'CMake (configure + build + tests) x64'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"     `
              -B build_x64                                        `
              -GNinja                                             `
              -DCMAKE_BUILD_TYPE=Debug                            `
              -Werror=dev                                         `
              -DTEST_FULL=TRUE                                    `
              -DTEST_STATIC=FALSE                                 `
              -DTEST_SHARED=TRUE                                  `
              -DTEST_TEST=TRUE                                    `
              -DCMAKE_SUPPRESS_REGENERATION=TRUE                  `
              -DCMAKE_PREFIX_PATH="${{ steps.bin.outputs.path }}"
          Start-Sleep -Seconds 2
          cmake --build build_x64 --config Release --verbose
          ctest --test-dir build_x64 --no-tests=error -C Release --output-on-failure
      - name: 'Configure vcvars arm64'
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: x64_arm64
      - name: 'CMake (configure + build) arm64'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"     `
              -B build_arm64                                      `
              -GNinja                                             `
              -DCMAKE_BUILD_TYPE=Debug                            `
              -Werror=dev                                         `
              -DTEST_FULL=TRUE                                    `
              -DTEST_STATIC=FALSE                                 `
              -DTEST_SHARED=TRUE                                  `
              -DTEST_TEST=TRUE                                    `
              -DCMAKE_SUPPRESS_REGENERATION=TRUE                  `
              -DCMAKE_PREFIX_PATH="${{ steps.bin.outputs.path }}"
          Start-Sleep -Seconds 2
          cmake --build build_arm64 --config Release --verbose
      - name: 'CMake (configure + build) arm64ec'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"     `
              -B build_arm64ec                                    `
              -GNinja                                             `
              -DCMAKE_BUILD_TYPE=Debug                            `
              -Werror=dev                                         `
              -DTEST_FULL=TRUE                                    `
              -DTEST_STATIC=FALSE                                 `
              -DTEST_SHARED=TRUE                                  `
              -DTEST_TEST=TRUE                                    `
              -DSDL_DISABLE_AVX=TRUE                              `
              -DSDL_DISABLE_AVX2=TRUE                             `
              -DSDL_DISABLE_AVX512F=TRUE                          `
              -DCMAKE_SUPPRESS_REGENERATION=TRUE                  `
              -DCMAKE_C_FLAGS="/arm64EC" `
              -DCMAKE_CXX_FLAGS="/arm64EC" `
              -DCMAKE_EXE_LINKER_FLAGS="/MACHINE:ARM64EC"         `
              -DCMAKE_SHARED_LINKER_FLAGS="/MACHINE:ARM64EC"      `
              -DCMAKE_STATIC_LINKER_FLAGS="/MACHINE:ARM64EC"      `
              -DCMAKE_PREFIX_PATH="${{ steps.bin.outputs.path }}"
          Start-Sleep -Seconds 2
          cmake --build build_arm64ec --config Release --verbose

  mingw:
    needs: [src]
    runs-on: ubuntu-24.04  # FIXME: current ubuntu-latest ships an outdated mingw, replace with ubuntu-latest once 24.04 becomes the new default
    outputs:
      mingw-devel-tar-gz: ${{ steps.releaser.outputs.mingw-devel-tar-gz }}
      mingw-devel-tar-xz: ${{ steps.releaser.outputs.mingw-devel-tar-xz }}
    steps:
      - name: 'Set up Python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: 'Fetch build-release.py'
        uses: actions/checkout@v4
        with:
          sparse-checkout: 'build-scripts/build-release.py'
      - name: 'Install Mingw toolchain'
        run: |
          sudo apt-get update -y
          sudo apt-get install -y gcc-mingw-w64 g++-mingw-w64 ninja-build
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: tar
        run: |
          mkdir -p /tmp/tardir
          tar -C /tmp/tardir -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=/tmp/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Build MinGW binary archives'
        id: releaser
        run: |
          python build-scripts/build-release.py \
            --actions mingw \
            --commit ${{ inputs.commit }} \
            --root "${{ steps.tar.outputs.path }}" \
            --github \
            --debug
      - name: 'Store MinGW archives'
        uses: actions/upload-artifact@v4
        with:
          name: mingw
          path: '${{ github.workspace }}/dist'

  mingw-verify:
    needs: [mingw, src]
    runs-on: ubuntu-latest
    steps:
      - name: 'Install Mingw toolchain'
        run: |
          sudo apt-get update -y
          sudo apt-get install -y gcc-mingw-w64 g++-mingw-w64 ninja-build
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Download MinGW binaries'
        uses: actions/download-artifact@v4
        with:
          name: mingw
          path: '${{ github.workspace }}'
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: src
        run: |
          mkdir -p /tmp/tardir
          tar -C /tmp/tardir -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=/tmp/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Untar ${{ needs.mingw.outputs.mingw-devel-tar-gz }}'
        id: bin
        run: |
          mkdir -p /tmp/mingw-tardir
          tar -C /tmp/mingw-tardir -v -x -f "${{ github.workspace }}/${{ needs.mingw.outputs.mingw-devel-tar-gz }}"
          echo "path=/tmp/mingw-tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'CMake (configure + build) i686'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"                                                           \
              -DCMAKE_BUILD_TYPE="Release"                                                                              \
              -DTEST_FULL=TRUE                                                                                          \
              -DTEST_STATIC=FALSE                                                                                       \
              -DTEST_TEST=TRUE                                                                                          \
              -DCMAKE_PREFIX_PATH="${{ steps.bin.outputs.path }}"                                                       \
              -DCMAKE_TOOLCHAIN_FILE="${{ steps.src.outputs.path }}/build-scripts/cmake-toolchain-mingw64-i686.cmake"   \
              -DCMAKE_C_FLAGS="-DSDL_DISABLE_SSE4_2"                                                                    \
              -Werror=dev                                                                                               \
              -B build_x86
          cmake --build build_x86 --config Release --verbose
      - name: 'CMake (configure + build) x86_64'
        run: |
          cmake -S "${{ steps.src.outputs.path }}/cmake/test"                                                           \
              -DCMAKE_BUILD_TYPE="Release"                                                                              \
              -DTEST_FULL=TRUE                                                                                          \
              -DTEST_STATIC=false                                                                                       \
              -DTEST_TEST=TRUE                                                                                          \
              -DCMAKE_PREFIX_PATH="${{ steps.bin.outputs.path }}"                                                       \
              -DCMAKE_TOOLCHAIN_FILE="${{ steps.src.outputs.path }}/build-scripts/cmake-toolchain-mingw64-x86_64.cmake" \
              -DCMAKE_C_FLAGS="-DSDL_DISABLE_SSE4_2"                                                                    \
              -Werror=dev                                                                                               \
              -B build_x64
          cmake --build build_x64 --config Release --verbose

  android:
    needs: [src]
    runs-on: ubuntu-latest
    outputs:
      android-aar: ${{ steps.releaser.outputs.android-aar }}
    steps:
      - name: 'Set up Python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: 'Fetch build-release.py'
        uses: actions/checkout@v4
        with:
          sparse-checkout: 'build-scripts/build-release.py'
      - name: 'Setup Android NDK'
        id: setup-ndk
        uses: nttld/setup-ndk@v1
        with:
          local-cache: true
          ndk-version: r21e
      - name: 'Setup Java JDK'
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '11'
      - name: 'Install ninja'
        run: |
          sudo apt-get update -y
          sudo apt-get install -y ninja-build
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: tar
        run: |
          mkdir -p /tmp/tardir
          tar -C /tmp/tardir -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=/tmp/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Build Android prefab binary archive(s)'
        id: releaser
        run: |
          python build-scripts/build-release.py \
            --actions android \
            --android-api 23 \
            --android-ndk-home "${{ steps.setup-ndk.outputs.ndk-path }}" \
            --commit ${{ inputs.commit }} \
            --root "${{ steps.tar.outputs.path }}" \
            --github \
            --debug
      - name: 'Store Android archive(s)'
        uses: actions/upload-artifact@v4
        with:
          name: android
          path: '${{ github.workspace }}/dist'

  android-verify:
    needs: [android, src]
    runs-on: ubuntu-latest
    steps:
      - name: 'Set up Python'
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: 'Download source archives'
        uses: actions/download-artifact@v4
        with:
          name: sources
          path: '${{ github.workspace }}'
      - name: 'Download Android .aar archive'
        uses: actions/download-artifact@v4
        with:
          name: android
          path: '${{ github.workspace }}'
      - name: 'Untar ${{ needs.src.outputs.src-tar-gz }}'
        id: src
        run: |
          mkdir -p /tmp/tardir
          tar -C /tmp/tardir -v -x -f "${{ github.workspace }}/${{ needs.src.outputs.src-tar-gz }}"
          echo "path=/tmp/tardir/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}" >>$GITHUB_OUTPUT
      - name: 'Extract Android SDK from AAR'
        id: sdk
        run: |
          cd /tmp
          unzip "${{ github.workspace }}/${{ needs.android.outputs.android-aar }}"
          python "${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}.aar" -o /tmp/SDL3-android
          echo "prefix=/tmp/SDL3-android" >>$GITHUB_OUTPUT
          echo "sdl3-aar=/tmp/${{ needs.src.outputs.project }}-${{ needs.src.outputs.version }}.aar" >>$GITHUB_OUTPUT
      - name: 'CMake (configure + build) x86, x64, arm32, arm64'
        run: |
          android_abis="x86 x86_64 armeabi-v7a arm64-v8a"
          for android_abi in ${android_abis}; do
            echo "Configuring ${android_abi}..."
            cmake -S "${{ steps.src.outputs.path }}/cmake/test" \
              -DTEST_FULL=TRUE \
              -DTEST_STATIC=FALSE \
              -DTEST_TEST=TRUE \
              -DCMAKE_PREFIX_PATH="${{ steps.sdk.outputs.prefix }}" \
              -DCMAKE_TOOLCHAIN_FILE=${ANDROID_NDK_HOME}/build/cmake/android.toolchain.cmake \
              -DANDROID_ABI=${android_abi} \
              -DCMAKE_BUILD_TYPE=Release \
              -B "${android_abi}"
            echo "Building ${android_abi}..."
            cmake --build "${android_abi}" --config Release --verbose
          done
      - name: 'Create gradle project'
        id: create-gradle-project
        run: |
          python ${{ steps.src.outputs.path }}/build-scripts/create-android-project.py \
            org.libsdl.testspriteminimal \
            ${{ steps.src.outputs.path }}/test/testspriteminimal.c \
            ${{ steps.src.outputs.path }}/test/icon.h \
            --variant aar \
            --output "/tmp/projects"
          echo "path=/tmp/projects/org.libsdl.testspriteminimal" >>$GITHUB_OUTPUT
      - name: 'Copy SDL3 aar into Gradle project'
        run: |
          cp "${{ steps.sdk.outputs.sdl3-aar }}" "${{ steps.create-gradle-project.outputs.path }}/app/libs"

          echo ""
          echo "Project contents:"
          echo ""
          find "${{ steps.create-gradle-project.outputs.path }}"
      - name: 'Build app (Gradle & CMake)'
        run: |
          cd "${{ steps.create-gradle-project.outputs.path }}"
          ./gradlew -i assembleRelease -Pandroid.native.buildOutput=verbose -PBUILD_WITH_CMAKE=1
      - name: 'Build app (Gradle & ndk-build)'
        run: |
          cd "${{ steps.create-gradle-project.outputs.path }}"
          ./gradlew -i assembleRelease -Pandroid.native.buildOutput=verbose
